# Advanced Binance Trading Bot with Discord Integration

A sophisticated cryptocurrency trading bot that implements a multi-indicator strategy with Discord notifications. This bot replaces the previous Telegram integration with Discord and implements an advanced trading strategy based on Bollinger Bands, MACD, RSI, and volume analysis.

## 🚀 Features

### New Strategy Implementation
- **Multi-Indicator Confirmation System**: Combines 5 technical indicators for high-probability signals
- **Bollinger Band Squeeze Detection**: Identifies low-volatility periods before breakouts
- **MACD Momentum Confirmation**: Ensures momentum supports the breakout
- **Volume Analysis**: Confirms breakout strength with volume spikes
- **RSI Overextension Check**: Prevents buying at market tops

### Discord Integration
- **Real-time Notifications**: Trading signals, alerts, and P&L updates
- **Slash Commands**: `/balance`, `/profit`, `/status`, `/strategy`
- **Rich Embeds**: Beautiful formatted messages with trading data
- **Interactive Commands**: Get bot status and strategy analysis

### Technical Features
- **Native Binance REST API**: Direct API calls without npm packages
- **WebSocket Real-time Data**: Live price monitoring and trade execution
- **Modular Architecture**: Clean, maintainable code structure
- **Comprehensive Testing**: Unit tests for all major components
- **Graceful Shutdown**: Proper cleanup on exit signals

## 📋 Strategy Conditions

The bot triggers BUY signals only when ALL 5 conditions are met:

1. **Market Squeeze**: Bollinger Band Width at 6-month low
2. **Breakout**: Price closes above Upper Bollinger Band
3. **Momentum**: MACD line above Signal line
4. **Volume**: Current volume 50%+ higher than 20-period average
5. **Safety**: RSI below 70 (not overextended)

## 🛠️ Installation

### Prerequisites
- Node.js 16+ 
- Discord Bot Token
- Discord Server with appropriate permissions

### Setup Steps

1. **Clone and Install**
   ```bash
   git clone <repository-url>
   cd binance-bot
   npm install
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` with your Discord credentials:
   ```env
   DISCORD_BOT_TOKEN=your_discord_bot_token_here
   DISCORD_CHANNEL_ID=your_discord_channel_id_here
   DISCORD_GUILD_ID=your_discord_guild_id_here
   LOG_LEVEL=info
   ```

3. **Discord Bot Setup**
   - Create a Discord application at https://discord.com/developers/applications
   - Create a bot and copy the token
   - Invite bot to your server with appropriate permissions:
     - Send Messages
     - Use Slash Commands
     - Embed Links
   - Get your server (guild) ID and channel ID

4. **Run the Bot**
   ```bash
   npm start
   ```

## 🧪 Testing

Run the test suite:
```bash
npm test
```

Run tests in watch mode:
```bash
npm run test:watch
```

## 📁 Project Structure

```
binance-bot/
├── index.js              # Main bot entry point
├── config.js             # Configuration management
├── binance-api.js         # Binance REST API & WebSocket
├── discord-bot.js         # Discord bot integration
├── indicators.js          # Technical indicators calculations
├── strategy.js            # Trading strategy implementation
├── test/                  # Test files
│   ├── indicators.test.js
│   └── binance-api.test.js
├── package.json
├── .env.example
└── README.md
```

## 🎮 Discord Commands

- `/balance` - Get current trading balance
- `/profit` - Show profit/loss since start
- `/status` - Detailed bot status and active positions
- `/strategy <symbol>` - Strategy analysis for specific symbol

## ⚙️ Configuration

Edit `config.js` to customize:

- **Trading Symbols**: Add/remove trading pairs
- **Strategy Parameters**: Bollinger Bands, MACD, RSI settings
- **Risk Management**: Position sizing, stop losses
- **Update Intervals**: Data refresh rates

## 🔧 Key Changes from Previous Version

### Removed
- ❌ Telegram Bot API dependency
- ❌ `binance` npm package
- ❌ `request-promise` (deprecated)
- ❌ Simple Bollinger Bands strategy

### Added
- ✅ Discord.js integration
- ✅ Native Binance REST API calls
- ✅ Multi-indicator strategy
- ✅ Comprehensive testing
- ✅ Modular architecture
- ✅ Environment configuration
- ✅ Graceful error handling

## 📊 Strategy Performance

The new strategy focuses on:
- **Higher Accuracy**: Multiple confirmation signals
- **Lower False Positives**: Squeeze detection prevents random signals
- **Better Risk Management**: RSI prevents buying at tops
- **Volume Confirmation**: Ensures institutional participation

## 🚨 Risk Disclaimer

This bot is for educational and simulation purposes. Cryptocurrency trading involves significant risk. Always:
- Test thoroughly before live trading
- Use proper risk management
- Never invest more than you can afford to lose
- Understand the strategy before deployment

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 📝 License

ISC License - see package.json for details

## 🆘 Support

For issues and questions:
1. Check the test files for usage examples
2. Review the configuration options
3. Ensure Discord bot permissions are correct
4. Verify environment variables are set

---

**Happy Trading! 🚀📈**
