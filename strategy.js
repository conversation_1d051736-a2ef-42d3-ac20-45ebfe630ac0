const TechnicalIndicators = require('./indicators');

class TradingStrategy {
  constructor(config, discordBot) {
    this.config = config;
    this.discordBot = discordBot;
    this.indicators = new TechnicalIndicators(config);
    
    // Store historical data for each symbol
    this.marketData = new Map();
    this.bbwHistory = new Map();
    this.alertStates = new Map();
    
    // Initialize data structures for tracked symbols
    config.trading.symbols.forEach(symbol => {
      this.marketData.set(symbol, {
        prices: [],
        volumes: [],
        klines: [],
        lastUpdate: 0,
      });
      this.bbwHistory.set(symbol, []);
      this.alertStates.set(symbol, { alert: false, lastAlertTime: 0 });
    });
  }

  /**
   * Update market data for a symbol
   * @param {string} symbol - Trading pair symbol
   * @param {Array} klineData - Kline data from Binance API
   */
  updateMarketData(symbol, klineData) {
    if (!klineData || klineData.length === 0) return;

    const data = this.marketData.get(symbol);
    if (!data) return;

    // Extract prices and volumes from kline data
    const prices = klineData.map(kline => kline.close);
    const volumes = klineData.map(kline => kline.volume);

    // Update stored data
    data.prices = prices;
    data.volumes = volumes;
    data.klines = klineData;
    data.lastUpdate = Date.now();

    // Update BBW history for squeeze detection
    this.updateBBWHistory(symbol, prices);
  }

  /**
   * Update Bollinger Band Width history
   * @param {string} symbol - Trading pair symbol
   * @param {Array<number>} prices - Price data
   */
  updateBBWHistory(symbol, prices) {
    const bollingerBands = this.indicators.calculateBollingerBands(
      prices,
      this.config.strategy.bollingerBands.period,
      this.config.strategy.bollingerBands.stdDev
    );

    if (bollingerBands) {
      const bbw = this.indicators.calculateBollingerBandWidth(bollingerBands);
      if (bbw !== null) {
        const history = this.bbwHistory.get(symbol);
        history.push(bbw);
        
        // Keep only the last 6 months of data (assuming daily updates)
        const maxHistory = this.config.strategy.bollingerBands.squeezeHistoryPeriod;
        if (history.length > maxHistory) {
          history.splice(0, history.length - maxHistory);
        }
      }
    }
  }

  /**
   * Analyze market conditions for a symbol
   * @param {string} symbol - Trading pair symbol
   * @returns {Object} Analysis result
   */
  analyzeSymbol(symbol) {
    const data = this.marketData.get(symbol);
    if (!data || data.prices.length === 0) {
      return { valid: false, reason: 'No market data available' };
    }

    const currentPrice = data.prices[data.prices.length - 1];
    const analysis = this.indicators.analyzeMarket({
      prices: data.prices,
      volumes: data.volumes,
      currentPrice,
    });

    if (!analysis.valid) {
      return analysis;
    }

    // Add squeeze detection using historical BBW data
    const bbwHistory = this.bbwHistory.get(symbol);
    const isInSqueeze = this.indicators.isInSqueeze(
      bbwHistory,
      analysis.indicators.bbw,
      this.config.strategy.bollingerBands.squeezeHistoryPeriod
    );

    // Override condition 1 with proper squeeze detection
    analysis.conditions.condition1.met = isInSqueeze;
    
    // Recalculate if all conditions are met
    const allConditionsMet = Object.values(analysis.conditions)
      .filter(condition => condition.met !== undefined)
      .every(condition => condition.met);
    
    analysis.conditions.allConditionsMet = allConditionsMet;
    analysis.conditions.signal = allConditionsMet ? 'BUY' : 'WAIT';

    return {
      ...analysis,
      symbol,
      currentPrice,
      timestamp: Date.now(),
    };
  }

  /**
   * Check for trading signals
   * @param {string} symbol - Trading pair symbol
   * @param {number} currentPrice - Current price
   * @param {Object} simulator - Trading simulator instance
   */
  checkTradingSignal(symbol, currentPrice, simulator) {
    // Don't check if we already have an active order
    if (simulator.isHavingOrder) {
      return;
    }

    // Check minimum time between orders
    const timeSinceLastOrder = Date.now() - simulator.lastOrderTime;
    if (timeSinceLastOrder < this.config.trading.simulator.minOrderInterval) {
      return;
    }

    const analysis = this.analyzeSymbol(symbol);
    
    if (!analysis.valid || analysis.conditions.signal !== 'BUY') {
      return;
    }

    // Execute buy order
    this.executeBuyOrder(symbol, currentPrice, analysis, simulator);
  }

  /**
   * Execute a buy order
   * @param {string} symbol - Trading pair symbol
   * @param {number} price - Entry price
   * @param {Object} analysis - Market analysis data
   * @param {Object} simulator - Trading simulator instance
   */
  executeBuyOrder(symbol, price, analysis, simulator) {
    const bollingerBands = analysis.indicators.bollingerBands;
    if (!bollingerBands) return;

    // Calculate stop loss and take profit based on Bollinger Bands
    const bandWidth = bollingerBands.upper - bollingerBands.lower;
    const stopLoss = price - (bandWidth * 0.5); // Stop loss below entry
    const takeProfit = price + (bandWidth * 1.0); // Take profit above entry

    const amount = Math.floor((simulator.currentBalance / price) * 100) / 100; // Round to 2 decimals

    // Create order
    const order = {
      type: 'long',
      pair: symbol,
      price: price,
      stoploss: stopLoss,
      takeProfit: takeProfit,
      amount: amount,
    };

    // Update simulator state
    simulator.isHavingOrder = true;
    simulator.lastOrderTime = Date.now();
    simulator.order = order;

    // Send Discord notification
    this.discordBot.sendTradingSignal({
      type: 'BUY',
      symbol: symbol,
      price: price.toFixed(2),
      stoploss: stopLoss.toFixed(2),
      takeProfit: takeProfit.toFixed(2),
      amount: amount,
      conditions: analysis.conditions,
    });

    console.log(`BUY signal executed for ${symbol} at $${price}`);
  }

  /**
   * Check price alerts (Bollinger Band extremes)
   * @param {string} symbol - Trading pair symbol
   * @param {number} currentPrice - Current price
   */
  checkPriceAlerts(symbol, currentPrice) {
    const data = this.marketData.get(symbol);
    if (!data || data.prices.length === 0) return;

    const bollingerBands = this.indicators.calculateBollingerBands(
      data.prices,
      this.config.strategy.bollingerBands.period,
      this.config.strategy.bollingerBands.stdDev
    );

    if (!bollingerBands) return;

    const alertState = this.alertStates.get(symbol);
    const threshold = (bollingerBands.middle - bollingerBands.lower) / 2;

    // Check for extreme price movements
    const isExtremeHigh = currentPrice >= bollingerBands.upper + threshold;
    const isExtremeLow = currentPrice <= bollingerBands.lower - threshold;

    if ((isExtremeHigh || isExtremeLow) && !alertState.alert) {
      const alertType = isExtremeHigh ? 'Extreme High - Above Upper BB' : 'Extreme Low - Below Lower BB';
      
      this.discordBot.sendPriceAlert(symbol, currentPrice.toFixed(2), alertType);
      
      alertState.alert = true;
      alertState.lastAlertTime = Date.now();
      
      console.log(`Price alert for ${symbol}: ${alertType} at $${currentPrice}`);
    }

    // Reset alert state if price returns to normal range
    if (alertState.alert && currentPrice > bollingerBands.lower && currentPrice < bollingerBands.upper) {
      alertState.alert = false;
    }
  }

  /**
   * Check if current position should be closed
   * @param {Object} order - Current order
   * @param {number} currentPrice - Current price
   * @param {Object} simulator - Trading simulator instance
   */
  checkPositionExit(order, currentPrice, simulator) {
    if (!order || !simulator.isHavingOrder) return;

    let shouldClose = false;
    let exitReason = '';

    if (order.type === 'long') {
      if (currentPrice >= order.takeProfit) {
        shouldClose = true;
        exitReason = 'Take Profit';
      } else if (currentPrice <= order.stoploss) {
        shouldClose = true;
        exitReason = 'Stop Loss';
      }
    }

    if (shouldClose) {
      this.closePosition(order, currentPrice, simulator, exitReason);
    }
  }

  /**
   * Close current position
   * @param {Object} order - Current order
   * @param {number} exitPrice - Exit price
   * @param {Object} simulator - Trading simulator instance
   * @param {string} reason - Exit reason
   */
  closePosition(order, exitPrice, simulator, reason) {
    // Calculate profit/loss
    let profit = order.amount * (exitPrice - order.price);
    if (order.type === 'short') {
      profit = -profit;
    }

    // Update simulator balance
    simulator.currentBalance += profit;

    // Send Discord notification
    this.discordBot.sendProfitLossNotification(order, exitPrice, profit);

    // Reset simulator state
    simulator.isHavingOrder = false;
    simulator.order = {};

    console.log(`Position closed for ${order.pair}: ${reason} at $${exitPrice}, P&L: $${profit.toFixed(2)}`);
  }

  /**
   * Get strategy status for a symbol
   * @param {string} symbol - Trading pair symbol
   * @returns {Object} Strategy status
   */
  getStrategyStatus(symbol) {
    const analysis = this.analyzeSymbol(symbol);
    const data = this.marketData.get(symbol);
    
    return {
      symbol,
      lastUpdate: data ? new Date(data.lastUpdate).toISOString() : null,
      analysis,
      dataPoints: data ? data.prices.length : 0,
      bbwHistoryLength: this.bbwHistory.get(symbol)?.length || 0,
    };
  }
}

module.exports = TradingStrategy;
