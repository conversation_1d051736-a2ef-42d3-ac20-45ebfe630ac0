const axios = require('axios');
const WebSocket = require('ws');
const config = require('./config');

class BinanceAPI {
  constructor() {
    this.baseUrl = config.binance.baseUrl;
    this.wsUrl = config.binance.wsUrl;
    this.ws = null;
    this.subscriptions = new Map();
  }

  /**
   * Get kline/candlestick data from Binance REST API
   * @param {string} symbol - Trading pair symbol (e.g., 'BTCUSDT')
   * @param {string} interval - Kline interval (e.g., '1m', '5m', '1h')
   * @param {number} limit - Number of klines to retrieve (max 1000)
   * @returns {Promise<Array>} Array of kline data
   */
  async getKlines(symbol, interval, limit = 500) {
    try {
      const url = `${this.baseUrl}/api/v3/klines`;
      const params = {
        symbol: symbol.toUpperCase(),
        interval,
        limit,
      };

      const response = await axios.get(url, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching klines for ${symbol}:`, error.message);
      throw error;
    }
  }

  /**
   * Get 24hr ticker statistics
   * @param {string} symbol - Trading pair symbol
   * @returns {Promise<Object>} Ticker data
   */
  async getTicker24hr(symbol) {
    try {
      const url = `${this.baseUrl}/api/v3/ticker/24hr`;
      const params = { symbol: symbol.toUpperCase() };

      const response = await axios.get(url, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching 24hr ticker for ${symbol}:`, error.message);
      throw error;
    }
  }

  /**
   * Get current price for a symbol
   * @param {string} symbol - Trading pair symbol
   * @returns {Promise<Object>} Price data
   */
  async getPrice(symbol) {
    try {
      const url = `${this.baseUrl}/api/v3/ticker/price`;
      const params = { symbol: symbol.toUpperCase() };

      const response = await axios.get(url, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching price for ${symbol}:`, error.message);
      throw error;
    }
  }

  /**
   * Connect to Binance WebSocket stream
   * @param {Array<string>} streams - Array of stream names
   * @param {Function} onMessage - Callback for incoming messages
   */
  connectWebSocket(streams, onMessage) {
    if (this.ws) {
      this.ws.close();
    }

    const streamNames = streams.join('/');
    const wsUrl = `${this.wsUrl}/${streamNames}`;

    this.ws = new WebSocket(wsUrl);

    this.ws.on('open', () => {
      console.log('WebSocket connected to Binance');
    });

    this.ws.on('message', (data) => {
      try {
        const message = JSON.parse(data);
        onMessage(message);
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    });

    this.ws.on('error', (error) => {
      console.error('WebSocket error:', error);
    });

    this.ws.on('close', () => {
      console.log('WebSocket connection closed');
      // Attempt to reconnect after 5 seconds
      setTimeout(() => {
        console.log('Attempting to reconnect WebSocket...');
        this.connectWebSocket(streams, onMessage);
      }, 5000);
    });
  }

  /**
   * Subscribe to trade streams for multiple symbols
   * @param {Array<string>} symbols - Array of trading pair symbols
   * @param {Function} onTrade - Callback for trade events
   */
  subscribeToTrades(symbols, onTrade) {
    const streams = symbols.map(symbol => `${symbol.toLowerCase()}@trade`);
    this.connectWebSocket(streams, (message) => {
      if (message.e === 'trade') {
        onTrade({
          symbol: message.s,
          price: parseFloat(message.p),
          quantity: parseFloat(message.q),
          time: message.T,
          isBuyerMaker: message.m,
        });
      }
    });
  }

  /**
   * Subscribe to kline streams for multiple symbols
   * @param {Array<string>} symbols - Array of trading pair symbols
   * @param {string} interval - Kline interval
   * @param {Function} onKline - Callback for kline events
   */
  subscribeToKlines(symbols, interval, onKline) {
    const streams = symbols.map(symbol => `${symbol.toLowerCase()}@kline_${interval}`);
    this.connectWebSocket(streams, (message) => {
      if (message.e === 'kline') {
        const kline = message.k;
        onKline({
          symbol: kline.s,
          openTime: kline.t,
          closeTime: kline.T,
          open: parseFloat(kline.o),
          high: parseFloat(kline.h),
          low: parseFloat(kline.l),
          close: parseFloat(kline.c),
          volume: parseFloat(kline.v),
          quoteVolume: parseFloat(kline.q),
          trades: kline.n,
          isClosed: kline.x,
        });
      }
    });
  }

  /**
   * Close WebSocket connection
   */
  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  /**
   * Parse kline data from REST API response
   * @param {Array} klineData - Raw kline data from API
   * @returns {Object} Parsed kline data
   */
  parseKlineData(klineData) {
    return klineData.map(kline => ({
      openTime: kline[0],
      open: parseFloat(kline[1]),
      high: parseFloat(kline[2]),
      low: parseFloat(kline[3]),
      close: parseFloat(kline[4]),
      volume: parseFloat(kline[5]),
      closeTime: kline[6],
      quoteVolume: parseFloat(kline[7]),
      trades: kline[8],
      takerBuyBaseVolume: parseFloat(kline[9]),
      takerBuyQuoteVolume: parseFloat(kline[10]),
    }));
  }
}

module.exports = BinanceAPI;
