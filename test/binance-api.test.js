const BinanceAPI = require('../binance-api');
const axios = require('axios');

// Mock axios
jest.mock('axios');
const mockedAxios = axios;

describe('BinanceAPI', () => {
  let binanceAPI;

  beforeEach(() => {
    binanceAPI = new BinanceAPI();
    jest.clearAllMocks();
  });

  describe('getKlines', () => {
    test('should fetch klines successfully', async () => {
      const mockKlineData = [
        [
          1499040000000,      // Open time
          "0.01634790",       // Open
          "0.80000000",       // High
          "0.01575800",       // Low
          "0.01577100",       // Close
          "148976.11427815",  // Volume
          1499644799999,      // Close time
          "2434.19055334",    // Quote asset volume
          308,                // Number of trades
          "1756.87402397",    // Taker buy base asset volume
          "28.46694368",      // Taker buy quote asset volume
          "0"                 // Unused field
        ]
      ];

      mockedAxios.get.mockResolvedValue({ data: mockKlineData });

      const result = await binanceAPI.getKlines('BTCUSDT', '5m', 100);

      expect(mockedAxios.get).toHaveBeenCalledWith(
        'https://api.binance.com/api/v3/klines',
        {
          params: {
            symbol: 'BTCUSDT',
            interval: '5m',
            limit: 100,
          },
        }
      );
      expect(result).toEqual(mockKlineData);
    });

    test('should handle API errors', async () => {
      const errorMessage = 'API Error';
      mockedAxios.get.mockRejectedValue(new Error(errorMessage));

      await expect(binanceAPI.getKlines('BTCUSDT', '5m', 100))
        .rejects.toThrow(errorMessage);
    });
  });

  describe('getTicker24hr', () => {
    test('should fetch 24hr ticker successfully', async () => {
      const mockTickerData = {
        symbol: 'BTCUSDT',
        priceChange: '-94.99999800',
        priceChangePercent: '-95.960',
        weightedAvgPrice: '0.29628482',
        prevClosePrice: '0.10002000',
        lastPrice: '4.00000200',
        volume: '8913.30000000',
        quoteVolume: '15.30000000',
      };

      mockedAxios.get.mockResolvedValue({ data: mockTickerData });

      const result = await binanceAPI.getTicker24hr('BTCUSDT');

      expect(mockedAxios.get).toHaveBeenCalledWith(
        'https://api.binance.com/api/v3/ticker/24hr',
        {
          params: {
            symbol: 'BTCUSDT',
          },
        }
      );
      expect(result).toEqual(mockTickerData);
    });
  });

  describe('getPrice', () => {
    test('should fetch current price successfully', async () => {
      const mockPriceData = {
        symbol: 'BTCUSDT',
        price: '43000.00000000',
      };

      mockedAxios.get.mockResolvedValue({ data: mockPriceData });

      const result = await binanceAPI.getPrice('BTCUSDT');

      expect(mockedAxios.get).toHaveBeenCalledWith(
        'https://api.binance.com/api/v3/ticker/price',
        {
          params: {
            symbol: 'BTCUSDT',
          },
        }
      );
      expect(result).toEqual(mockPriceData);
    });
  });

  describe('parseKlineData', () => {
    test('should parse kline data correctly', () => {
      const rawKlineData = [
        [
          1499040000000,      // Open time
          "0.01634790",       // Open
          "0.80000000",       // High
          "0.01575800",       // Low
          "0.01577100",       // Close
          "148976.11427815",  // Volume
          1499644799999,      // Close time
          "2434.19055334",    // Quote asset volume
          308,                // Number of trades
          "1756.87402397",    // Taker buy base asset volume
          "28.46694368",      // Taker buy quote asset volume
          "0"                 // Unused field
        ]
      ];

      const result = binanceAPI.parseKlineData(rawKlineData);

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        openTime: 1499040000000,
        open: 0.01634790,
        high: 0.80000000,
        low: 0.01575800,
        close: 0.01577100,
        volume: 148976.11427815,
        closeTime: 1499644799999,
        quoteVolume: 2434.19055334,
        trades: 308,
        takerBuyBaseVolume: 1756.87402397,
        takerBuyQuoteVolume: 28.46694368,
      });
    });

    test('should handle empty data', () => {
      const result = binanceAPI.parseKlineData([]);
      expect(result).toEqual([]);
    });
  });
});
