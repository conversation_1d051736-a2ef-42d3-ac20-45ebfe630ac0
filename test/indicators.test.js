const TechnicalIndicators = require('../indicators');

// Mock config for testing
const mockConfig = {
  strategy: {
    bollingerBands: {
      period: 20,
      stdDev: 2,
      squeezeThreshold: 0.1,
      squeezeHistoryPeriod: 180,
    },
    macd: {
      fastPeriod: 12,
      slowPeriod: 26,
      signalPeriod: 9,
    },
    rsi: {
      period: 14,
      overboughtThreshold: 70,
    },
    volume: {
      period: 20,
      breakoutMultiplier: 1.5,
    },
  },
};

describe('TechnicalIndicators', () => {
  let indicators;

  beforeEach(() => {
    indicators = new TechnicalIndicators(mockConfig);
  });

  describe('calculateBollingerBands', () => {
    test('should calculate Bollinger Bands correctly', () => {
      const prices = Array.from({ length: 30 }, (_, i) => 100 + Math.sin(i * 0.1) * 10);
      const result = indicators.calculateBollingerBands(prices, 20, 2);
      
      expect(result).toBeTruthy();
      expect(result).toHaveProperty('upper');
      expect(result).toHaveProperty('middle');
      expect(result).toHaveProperty('lower');
      expect(result.upper).toBeGreaterThan(result.middle);
      expect(result.middle).toBeGreaterThan(result.lower);
    });

    test('should return null for insufficient data', () => {
      const prices = [100, 101, 102];
      const result = indicators.calculateBollingerBands(prices, 20, 2);
      
      expect(result).toBeNull();
    });
  });

  describe('calculateBollingerBandWidth', () => {
    test('should calculate BBW correctly', () => {
      const bollingerBands = {
        upper: 110,
        middle: 100,
        lower: 90,
      };
      
      const bbw = indicators.calculateBollingerBandWidth(bollingerBands);
      expect(bbw).toBe(0.2); // (110 - 90) / 100 = 0.2
    });

    test('should return null for invalid input', () => {
      const bbw = indicators.calculateBollingerBandWidth(null);
      expect(bbw).toBeNull();
    });
  });

  describe('isInSqueeze', () => {
    test('should detect squeeze correctly', () => {
      const bbwHistory = Array.from({ length: 200 }, () => 0.15);
      bbwHistory[199] = 0.05; // Recent low
      
      const isInSqueeze = indicators.isInSqueeze(bbwHistory, 0.055, 180);
      expect(isInSqueeze).toBe(true);
    });

    test('should not detect squeeze when BBW is high', () => {
      const bbwHistory = Array.from({ length: 200 }, () => 0.15);
      
      const isInSqueeze = indicators.isInSqueeze(bbwHistory, 0.2, 180);
      expect(isInSqueeze).toBe(false);
    });
  });

  describe('calculateMACD', () => {
    test('should calculate MACD correctly', () => {
      const prices = Array.from({ length: 50 }, (_, i) => 100 + i * 0.5);
      const result = indicators.calculateMACD(prices, 12, 26, 9);
      
      expect(result).toBeTruthy();
      expect(result).toHaveProperty('MACD');
      expect(result).toHaveProperty('signal');
      expect(result).toHaveProperty('histogram');
    });

    test('should return null for insufficient data', () => {
      const prices = Array.from({ length: 20 }, (_, i) => 100 + i);
      const result = indicators.calculateMACD(prices, 12, 26, 9);
      
      expect(result).toBeNull();
    });
  });

  describe('calculateRSI', () => {
    test('should calculate RSI correctly', () => {
      const prices = Array.from({ length: 30 }, (_, i) => 100 + Math.sin(i * 0.2) * 20);
      const result = indicators.calculateRSI(prices, 14);
      
      expect(result).toBeTruthy();
      expect(typeof result).toBe('number');
      expect(result).toBeGreaterThanOrEqual(0);
      expect(result).toBeLessThanOrEqual(100);
    });

    test('should return null for insufficient data', () => {
      const prices = [100, 101, 102];
      const result = indicators.calculateRSI(prices, 14);
      
      expect(result).toBeNull();
    });
  });

  describe('isVolumeBreakout', () => {
    test('should detect volume breakout correctly', () => {
      const currentVolume = 1500;
      const averageVolume = 1000;
      
      const isBreakout = indicators.isVolumeBreakout(currentVolume, averageVolume, 1.5);
      expect(isBreakout).toBe(true);
    });

    test('should not detect breakout for normal volume', () => {
      const currentVolume = 1200;
      const averageVolume = 1000;
      
      const isBreakout = indicators.isVolumeBreakout(currentVolume, averageVolume, 1.5);
      expect(isBreakout).toBe(false);
    });
  });

  describe('checkStrategyConditions', () => {
    test('should check all conditions correctly', () => {
      const indicators_data = {
        bollingerBands: { upper: 110, middle: 100, lower: 90 },
        bbw: 0.05,
        macd: { MACD: 1.5, signal: 1.0, histogram: 0.5 },
        rsi: 65,
        averageVolume: 1000,
        currentVolume: 1600,
        currentPrice: 112,
      };

      const result = indicators.checkStrategyConditions(indicators_data);
      
      expect(result).toHaveProperty('condition1');
      expect(result).toHaveProperty('condition2');
      expect(result).toHaveProperty('condition3');
      expect(result).toHaveProperty('condition4');
      expect(result).toHaveProperty('condition5');
      expect(result).toHaveProperty('allConditionsMet');
      expect(result).toHaveProperty('signal');
      
      expect(result.condition1.met).toBe(true); // BBW low
      expect(result.condition2.met).toBe(true); // Price above upper BB
      expect(result.condition3.met).toBe(true); // MACD above signal
      expect(result.condition4.met).toBe(true); // Volume breakout
      expect(result.condition5.met).toBe(true); // RSI below 70
      expect(result.allConditionsMet).toBe(true);
      expect(result.signal).toBe('BUY');
    });
  });
});
