require('dotenv').config();

const config = {
  // Discord Bot Configuration
  discord: {
    token: process.env.DISCORD_BOT_TOKEN,
    channelId: process.env.DISCORD_CHANNEL_ID,
    guildId: process.env.DISCORD_GUILD_ID,
  },

  // Binance API Configuration
  binance: {
    baseUrl: 'https://api.binance.com',
    wsUrl: 'wss://stream.binance.com:9443/ws',
  },

  // Trading Configuration
  trading: {
    symbols: ['BTCUSDT', 'ETHUSDT'],
    intervals: {
      dataUpdate: 5, // minutes
      priceCheck: 1000, // milliseconds
    },
    simulator: {
      startingBalance: 100000,
      minOrderInterval: 300000, // 5 minutes in milliseconds
    },
  },

  // Strategy Configuration
  strategy: {
    bollingerBands: {
      period: 20,
      stdDev: 2,
      squeezeThreshold: 0.1, // BBW threshold for squeeze detection
      squeezeHistoryPeriod: 180, // 6 months in days (approximate)
    },
    macd: {
      fastPeriod: 12,
      slowPeriod: 26,
      signalPeriod: 9,
    },
    rsi: {
      period: 14,
      overboughtThreshold: 70,
    },
    volume: {
      period: 20,
      breakoutMultiplier: 1.5, // 50% higher than average
    },
  },

  // Logging Configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    enableConsole: true,
    enableFile: false,
  },
};

module.exports = config;
