const { Client, GatewayIntentBits } = require('discord.js');
require('dotenv').config();

const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
  ],
});

client.once('ready', () => {
  console.log(`✅ Bot logged in as ${client.user.tag}`);
  console.log('\n📋 Available Servers (Guilds):');
  
  client.guilds.cache.forEach(guild => {
    console.log(`  🏠 ${guild.name} (ID: ${guild.id})`);
    
    console.log(`    📝 Text Channels:`);
    guild.channels.cache
      .filter(channel => channel.isTextBased())
      .forEach(channel => {
        const permissions = channel.permissionsFor(client.user);
        const canSend = permissions.has(['ViewChannel', 'SendMessages']);
        console.log(`      ${canSend ? '✅' : '❌'} ${channel.name} (ID: ${channel.id})`);
      });
    console.log('');
  });
  
  console.log('💡 Copy the correct Guild ID and Channel ID to your .env file');
  process.exit(0);
});

client.on('error', (error) => {
  console.error('Discord client error:', error);
});

// Login with your bot token
client.login(process.env.DISCORD_BOT_TOKEN).catch(error => {
  console.error('Failed to login:', error);
  console.log('❌ Check your DISCORD_BOT_TOKEN in .env file');
});
